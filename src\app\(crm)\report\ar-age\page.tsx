import { ReportPage } from '@/lib';

import { getData } from './helpers';

interface PageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function ReportArAge({ searchParams }: PageProps) {
  const { data, chart } = await getData({ searchParams });

  return (
    <ReportPage
      data={data}
      axis={chart}
      title="A/R Age"
      description="Total account receivables and how long they have been waiting for collection."
    />
  );
}
