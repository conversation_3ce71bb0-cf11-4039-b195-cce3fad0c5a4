import { Clock } from 'lucide-react';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export async function RecentActivityWidget() {
  // In a real implementation, this would fetch data from your database
  const activities = [
    {
      title: 'New project created',
      description: 'Residential Roofing - 123 Main St',
      time: '2 hours ago',
      type: 'project' as const
    },
    {
      title: 'Quote approved',
      description: '$45,000 - Commercial Building',
      time: '4 hours ago',
      type: 'quote' as const
    },
    {
      title: 'Payment received',
      description: '$12,500 from Johnson Construction',
      time: '6 hours ago',
      type: 'payment' as const
    },
    {
      title: 'Report generated',
      description: 'Monthly Profitability Report',
      time: '1 day ago',
      type: 'report' as const
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'project': return 'bg-blue-100 text-blue-800';
      case 'quote': return 'bg-green-100 text-green-800';
      case 'payment': return 'bg-yellow-100 text-yellow-800';
      case 'report': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity, index) => (
          <div key={index} className="flex items-start space-x-3">
            <Badge variant="secondary" className={`${getTypeColor(activity.type)} text-xs`}>
              {activity.type}
            </Badge>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium">{activity.title}</p>
              <p className="text-xs text-muted-foreground">{activity.description}</p>
            </div>
            <span className="text-xs text-muted-foreground">{activity.time}</span>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
