'use client';

import { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { GripVertical, Search, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

interface ColumnOption {
  id: string;
  label: string;
  category: string;
  selected: boolean;
  description?: string;
}

interface ColumnSelectorProps {
  onClose: () => void;
  onApply: (selectedColumns: ColumnOption[]) => void;
}

export function ColumnSelector({ onClose, onApply }: ColumnSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedColumns, setSelectedColumns] = useState<ColumnOption[]>([
    { id: 'created-by', label: 'Created By', category: 'Job Information', selected: true },
    { id: 'permit-payment', label: 'Permit Payment Amount Sum', category: 'Financial', selected: true },
  ]);

  const [availableColumns] = useState<ColumnOption[]>([
    // Job Information
    { id: 'all-job', label: 'All', category: 'Job Information', selected: false },
    { id: 'created-by', label: 'Created By', category: 'Job Information', selected: true },
    { id: 'company-location', label: 'Company Location Name', category: 'Job Information', selected: false },
    { id: 'job-number', label: 'Job Number', category: 'Job Information', selected: false },
    { id: 'primary-contact-name', label: 'Primary Contact Name', category: 'Job Information', selected: false },
    { id: 'primary-contact-hire', label: 'Primary Contact Hire Name', category: 'Job Information', selected: false },
    { id: 'primary-contact-last', label: 'Primary Contact Last Name', category: 'Job Information', selected: false },
    { id: 'primary-contact-company', label: 'Primary Contact Company', category: 'Job Information', selected: false },
    { id: 'primary-contact-phone', label: 'Primary Contact Phone', category: 'Job Information', selected: false },
    { id: 'primary-contact-email', label: 'Primary Contact Email', category: 'Job Information', selected: false },
    { id: 'job-trade-type', label: 'Job Trade Type', category: 'Job Information', selected: false },
    { id: 'primary-contact-cross', label: 'Primary Contact Cross Reference', category: 'Job Information', selected: false },
    { id: 'job-tag', label: 'Job Tag', category: 'Job Information', selected: false },
    
    // Work Types
    { id: 'work-type', label: 'Work Type', category: 'Job Information', selected: false, description: 'Roofing, Repair, Service, Warranty' },
    { id: 'job-category', label: 'Job Category', category: 'Job Information', selected: false, description: 'Commercial, Property Management, Insurance' },
    { id: 'job-priority', label: 'Job Priority', category: 'Job Information', selected: false },
    { id: 'parent-lead-source', label: 'Parent Lead Source', category: 'Job Information', selected: false },
    { id: 'sub-lead-source', label: 'Sub Lead Source', category: 'Job Information', selected: false },
    { id: 'sales-owner', label: 'Sales Owner', category: 'Job Information', selected: false, description: 'Intended for Sales team data' },
    { id: 'ar-owner', label: 'A/R Owner', category: 'Job Information', selected: false, description: 'Intended for A/R team' },
    { id: 'dead-lead-reason', label: 'Dead Lead Reason', category: 'Job Information', selected: false },

    // Addresses
    { id: 'all-addresses', label: 'All', category: 'Addresses', selected: false },
    { id: 'job-location-address', label: 'Job Location Address', category: 'Addresses', selected: false },
    { id: 'job-location-street1', label: 'Job Location Street 1', category: 'Addresses', selected: false },
    { id: 'job-location-street2', label: 'Job Location Street 2', category: 'Addresses', selected: false },
    { id: 'primary-contact-billing-zip', label: 'Primary Contact Billing Zip Code', category: 'Addresses', selected: false },
    { id: 'primary-contact-billing-country', label: 'Primary Contact Billing Country', category: 'Addresses', selected: false },

    // Financial
    { id: 'permit-payment', label: 'Permit Payment Amount Sum', category: 'Financial', selected: true },
    { id: 'job-value', label: 'Job Value', category: 'Financial', selected: false },
    { id: 'total-invoiced', label: 'Total Invoiced', category: 'Financial', selected: false },
    { id: 'total-collected', label: 'Total Collected', category: 'Financial', selected: false },

    // Documentation
    { id: 'last-message-date', label: 'Last Message Date', category: 'Documentation', selected: false },
    
    // Milestones
    { id: 'current-milestone', label: 'Current Milestone', category: 'Milestones', selected: false },
    { id: 'lead-milestone-date', label: 'Lead Milestone Date', category: 'Milestones', selected: false },
    { id: 'prospect-milestone-date', label: 'Prospect Milestone Date', category: 'Milestones', selected: false },

    // Insurance
    { id: 'claim-filed', label: 'Claim Filed?', category: 'Insurance', selected: false },
    { id: 'claim-number', label: 'Claim Number', category: 'Insurance', selected: false },
  ]);

  const categories = Array.from(new Set(availableColumns.map(col => col.category)));

  const filteredColumns = availableColumns.filter(col =>
    col.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    col.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleColumn = (columnId: string) => {
    const column = availableColumns.find(col => col.id === columnId);
    if (!column) return;

    if (column.selected) {
      // Remove from selected
      setSelectedColumns(prev => prev.filter(col => col.id !== columnId));
    } else {
      // Add to selected
      setSelectedColumns(prev => [...prev, { ...column, selected: true }]);
    }

    // Update available columns
    column.selected = !column.selected;
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(selectedColumns);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSelectedColumns(items);
  };

  const handleApply = () => {
    onApply(selectedColumns);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Columns</h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Left Panel - Available Columns */}
          <div className="w-1/2 border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search Columns"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <ScrollArea className="flex-1">
              <div className="p-4">
                {categories.map(category => {
                  const categoryColumns = filteredColumns.filter(col => col.category === category);
                  if (categoryColumns.length === 0) return null;

                  return (
                    <div key={category} className="mb-6">
                      <h3 className="font-medium text-gray-900 mb-3 text-sm uppercase tracking-wide">
                        {category}
                      </h3>
                      <div className="space-y-2">
                        {categoryColumns.map(column => (
                          <div
                            key={column.id}
                            className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded cursor-pointer"
                            onClick={() => toggleColumn(column.id)}
                          >
                            <Checkbox
                              checked={column.selected}
                              onChange={() => toggleColumn(column.id)}
                              className="mt-0.5"
                            />
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium text-gray-900">
                                {column.label}
                              </div>
                              {column.description && (
                                <div className="text-xs text-gray-500 mt-1">
                                  {column.description}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Right Panel - Selected Columns */}
          <div className="w-1/2 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-medium text-gray-900">Selected Columns:</h3>
              <p className="text-sm text-gray-500 mt-1">Drag and drop to reorder</p>
            </div>

            <ScrollArea className="flex-1">
              <div className="p-4">
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="selected-columns">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className="space-y-2"
                      >
                        {selectedColumns.map((column, index) => (
                          <Draggable
                            key={column.id}
                            draggableId={column.id}
                            index={index}
                          >
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className={`flex items-center gap-3 p-3 bg-white border border-gray-200 rounded-lg ${
                                  snapshot.isDragging ? 'shadow-lg' : 'hover:bg-gray-50'
                                }`}
                              >
                                <div
                                  {...provided.dragHandleProps}
                                  className="text-gray-400 hover:text-gray-600"
                                >
                                  <GripVertical className="h-4 w-4" />
                                </div>
                                <div className="flex-1">
                                  <div className="text-sm font-medium text-gray-900">
                                    {column.label}
                                  </div>
                                  <Badge variant="secondary" className="text-xs mt-1">
                                    {column.category}
                                  </Badge>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleColumn(column.id)}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleApply} className="bg-blue-600 hover:bg-blue-700">
            Apply
          </Button>
        </div>
      </div>
    </div>
  );
}
