'use client';

import { useState } from 'react';
import { ChevronDown, ChevronRight, Plus, Edit, Settings, BarChart3, Filter, Palette, Users, Calculator, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

import { ColumnSelector } from './column-selector';
import { FilterBuilder } from './filter-builder';
import { GroupCalculator } from './group-calculator';

interface ReportBuilderProps {
  reportTitle: string;
  reportData: any[];
  onClose?: () => void;
}

export function ReportBuilder({ reportTitle, reportData, onClose }: ReportBuilderProps) {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['chart-options']));
  const [activeModal, setActiveModal] = useState<string | null>(null);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const openModal = (modalType: string) => {
    setActiveModal(modalType);
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  const configSections = [
    {
      id: 'chart-options',
      title: 'Chart Options',
      icon: BarChart3,
      action: 'Add',
      actionVariant: 'default' as const,
      description: 'Configure chart type and display options'
    },
    {
      id: 'columns',
      title: 'Columns',
      icon: Settings,
      action: 'Edit',
      actionVariant: 'secondary' as const,
      description: 'Select and reorder report columns',
      badge: '18 Selected'
    },
    {
      id: 'filters',
      title: 'Filters',
      icon: Filter,
      action: 'Add',
      actionVariant: 'default' as const,
      description: 'Add filters to narrow your results'
    },
    {
      id: 'color-codes',
      title: 'Color Codes',
      icon: Palette,
      action: 'Add',
      actionVariant: 'default' as const,
      description: 'Apply conditional formatting'
    },
    {
      id: 'group',
      title: 'Group',
      icon: Users,
      action: 'Edit',
      actionVariant: 'secondary' as const,
      description: 'Group data by specific fields',
      badge: 'Created By'
    },
    {
      id: 'group-calculations',
      title: 'Group Calculations',
      icon: Calculator,
      action: 'Add',
      actionVariant: 'default' as const,
      description: 'Add calculations for grouped data',
      badge: 'Permit Payment Amount Sum'
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-[90vh] flex overflow-hidden">
        {/* Left Sidebar - Configuration Panel */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Edit Report</h2>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

        <ScrollArea className="flex-1">
          <div className="p-4 space-y-2">
            {configSections.map((section) => {
              const Icon = section.icon;
              const isExpanded = expandedSections.has(section.id);

              return (
                <Collapsible
                  key={section.id}
                  open={isExpanded}
                  onOpenChange={() => toggleSection(section.id)}
                >
                  <CollapsibleTrigger asChild>
                    <div className="w-full">
                      <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Icon className="h-5 w-5 text-blue-600" />
                              <div className="flex flex-col">
                                <CardTitle className="text-sm font-medium text-gray-900">
                                  {section.title}
                                </CardTitle>
                                {section.badge && (
                                  <Badge variant="secondary" className="text-xs mt-1 w-fit">
                                    {section.badge}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant={section.actionVariant}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (section.id === 'columns') {
                                    openModal('column-selector');
                                  } else if (section.id === 'filters') {
                                    openModal('filter-builder');
                                  } else if (section.id === 'group-calculations') {
                                    openModal('group-calculator');
                                  } else {
                                    setActiveSection(section.id);
                                  }
                                }}
                              >
                                {section.action}
                              </Button>
                              {isExpanded ? (
                                <ChevronDown className="h-4 w-4 text-gray-500" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-gray-500" />
                              )}
                            </div>
                          </div>
                        </CardHeader>
                      </Card>
                    </div>
                  </CollapsibleTrigger>

                  <CollapsibleContent>
                    <div className="mt-2 ml-8 p-3 bg-gray-50 rounded-md">
                      <p className="text-sm text-gray-600">{section.description}</p>
                      {section.id === 'group' && (
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs">
                            Created By ×
                          </Badge>
                        </div>
                      )}
                      {section.id === 'group-calculations' && (
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs">
                            Permit Payment Amount Sum ×
                          </Badge>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              );
            })}
          </div>
        </ScrollArea>

        {/* Version History */}
        <div className="p-4 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Version History</h3>
          <div className="text-xs text-gray-500">
            <div>Sai Kiran Srisal Results</div>
            <div>05/14/2024</div>
            <div>2:24 PM</div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-semibold text-gray-900">{reportTitle}</h1>
              <Badge variant="secondary">BETA</Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">Help</Button>
              <Button variant="outline" size="sm">Save As</Button>
              <Button size="sm">Save</Button>
              <Button variant="outline" size="sm">
                Actions
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>

        {/* Report Configuration Bar */}
        <div className="bg-gray-100 border-b border-gray-200 p-3">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Date Range:</span>
              <select className="border border-gray-300 rounded px-2 py-1 text-sm">
                <option>This Year</option>
                <option>This Month</option>
                <option>Last Month</option>
                <option>Custom Range</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Reference Date:</span>
              <select className="border border-gray-300 rounded px-2 py-1 text-sm">
                <option>Prospect Milestone Date</option>
                <option>Created Date</option>
                <option>Last Modified Date</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Select Location:</span>
              <select className="border border-gray-300 rounded px-2 py-1 text-sm">
                <option>All Selected</option>
                <option>Main Office</option>
                <option>Branch Office</option>
              </select>
            </div>
          </div>
        </div>

        {/* Report Content */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="bg-white rounded-lg shadow">
            {/* Report Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">DESCRIPTION</h2>
                <Button variant="outline" size="sm">MY REPORT</Button>
              </div>
            </div>

            {/* Report Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-blue-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Created By
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Permit Payment Amount Sum
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-blue-600">Faith Hambrook</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">26,089</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-blue-600">Eric Mitchell</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">6,811</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-blue-600">Breck</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">4,055</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-blue-600">Faith</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">3,079</td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-blue-600">Patricia Vito</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">2,392</td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Report Footer */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="text-sm text-gray-600">
                Total Records: 334
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {activeModal === 'column-selector' && (
        <ColumnSelector
          onClose={closeModal}
          onApply={(columns) => {
            console.log('Applied columns:', columns);
            closeModal();
          }}
        />
      )}

      {activeModal === 'filter-builder' && (
        <FilterBuilder
          onClose={closeModal}
          onApply={(filters) => {
            console.log('Applied filters:', filters);
            closeModal();
          }}
        />
      )}

      {activeModal === 'group-calculator' && (
        <GroupCalculator
          onClose={closeModal}
          onApply={(calculations) => {
            console.log('Applied calculations:', calculations);
            closeModal();
          }}
        />
      )}
    </div>
  );
}
