import { cache } from 'react';

import { ChartAxis, ChartData } from '@/lib/chart';
import { tailwind } from '@/lib/theme';

import { validatePermissions } from './permissions';

export const getData = cache(async function ({
  searchParams
}: {
  searchParams?: { [key: string]: string | string[] | undefined }
} = {}) {
  'use server';

  const props = await validatePermissions();

  // Base data - in a real implementation, this would come from database queries
  // filtered by the searchParams (date range, locations, etc.)
  let baseData: ChartData[] = [
    ['0-30', 253848.36, { jobCount: 25, averageAge: 22 }, { color: tailwind.theme.colors.red['500'] }],
    ['30-60', 726228.35, { jobCount: 80, averageAge: 47 }, { color: tailwind.theme.colors.green['500'] }],
    ['60-90', 371080.03, { jobCount: 60, averageAge: 74 }, { color: tailwind.theme.colors.sky['500'] }],
    ['90-120', 352446.93, { jobCount: 72, averageAge: 106 }, { color: tailwind.theme.colors.yellow['500'] }],
    ['Older than 120', 700199.81, { jobCount: 270, averageAge: 351 }, { color: tailwind.theme.colors.purple['500'] }],
  ];

  // Apply filters based on search parameters
  if (searchParams) {
    const startDate = searchParams.startDate as string;
    const endDate = searchParams.endDate as string;
    const locations = searchParams.locations as string;
    const dateRange = searchParams.dateRange as string;

    // Simulate filtering effect by adjusting data
    if (dateRange && dateRange !== 'All Data') {
      // Simulate different data for different date ranges
      const multiplier = getDateRangeMultiplier(dateRange);
      baseData = baseData.map(([label, value, metadata, style]) => [
        label,
        (value as number) * multiplier,
        {
          ...metadata,
          jobCount: Math.round((metadata.jobCount as number) * multiplier),
        },
        style,
      ]);
    }

    if (locations) {
      // Simulate location filtering by reducing data proportionally
      const locationCount = locations.split(',').length;
      const locationMultiplier = Math.min(1, locationCount * 0.3 + 0.4); // 40% to 100% based on locations
      baseData = baseData.map(([label, value, metadata, style]) => [
        label,
        (value as number) * locationMultiplier,
        {
          ...metadata,
          jobCount: Math.round((metadata.jobCount as number) * locationMultiplier),
        },
        style,
      ]);
    }
  }

  const chart: ChartAxis = {
    y: {
      label: 'Total A/R',
    },
    x: {
      label: 'A/R Range (Days)',
    },
  };

  return { ...props, data: baseData, chart };
});

// Helper function to simulate different data for different date ranges
function getDateRangeMultiplier(dateRange: string): number {
  switch (dateRange) {
    case 'Today':
      return 0.1;
    case 'Yesterday':
      return 0.08;
    case 'This Week (Sun)':
    case 'This Week (Mon)':
      return 0.3;
    case 'Last Week (Sun)':
    case 'Last Week (Mon)':
      return 0.25;
    case 'This Month':
      return 0.8;
    case 'Last Month':
      return 0.75;
    case 'This Year':
      return 1.0;
    case 'Last Year':
      return 0.9;
    case 'Rolling 7 Days':
      return 0.35;
    case 'Rolling 30 Days':
      return 0.85;
    case 'Rolling 90 Days':
      return 0.95;
    case 'Rolling 365 Days':
      return 1.0;
    default:
      return 1.0;
  }
}
