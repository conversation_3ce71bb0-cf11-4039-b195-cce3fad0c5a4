import { cache } from 'react';

import { ChartAxis, ChartData } from '@/lib/chart';
import { tailwind } from '@/lib/theme';

import { validatePermissions } from './permissions';

export const getData = cache(async function () {
  'use server';

  const props = await validatePermissions();

  const data: ChartData[] = [
    ['0-30', 253848.36, { jobCount: 25, averageAge: 22 }, { color: tailwind.theme.colors.red['500'] }],
    ['30-60', 726228.35, { jobCount: 80, averageAge: 47 }, { color: tailwind.theme.colors.green['500'] }],
    ['60-90', 371080.03, { jobCount: 60, averageAge: 74 }, { color: tailwind.theme.colors.sky['500'] }],
    ['90-120', 352446.93, { jobCount: 72, averageAge: 106 }, { color: tailwind.theme.colors.yellow['500'] }],
    ['Older than 120', 700199.81, { jobCount: 270, averageAge: 351 }, { color: tailwind.theme.colors.purple['500'] }],
  ];

  const chart: ChartAxis = {
    y: {
      label: 'Total A/R',
    },
    x: {
      label: 'A/R Range (Days)',
    },
  };

  return { ...props, data, chart };
});
