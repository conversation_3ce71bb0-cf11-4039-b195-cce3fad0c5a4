import Link from 'next/link';
import { ArrowLeft, BarChart3, Calendar, DollarSign, TrendingUp, Users, FileText, Clock, Target } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';

import { getDashboardData } from './helpers';

export default async function ReportsDashboard() {
  const data = await getDashboardData();
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/report">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reports
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Reports Dashboard</h1>
            <p className="text-muted-foreground">Real-time business performance overview</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Last 30 Days
          </Button>
          <Button size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Export Dashboard
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <KPICard
          title="Total Revenue"
          value={data.kpis.totalRevenue}
          change={data.kpis.revenueChange}
          changeType="positive"
          icon={<DollarSign className="h-5 w-5" />}
          description="estimated total"
        />
        <KPICard
          title="Active Projects"
          value={data.kpis.activeProjects.toString()}
          change={data.kpis.projectsChange}
          changeType="positive"
          icon={<BarChart3 className="h-5 w-5" />}
          description="in progress"
        />
        <KPICard
          title="Conversion Rate"
          value={data.kpis.conversionRate}
          change={data.kpis.conversionChange}
          changeType="positive"
          icon={<TrendingUp className="h-5 w-5" />}
          description="completion rate"
        />
        <KPICard
          title="Team Members"
          value={data.kpis.teamMembers.toString()}
          change={data.kpis.teamChange}
          changeType="positive"
          icon={<Users className="h-5 w-5" />}
          description="active users"
        />
      </div>

      {/* Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales Performance</TabsTrigger>
          <TabsTrigger value="production">Production Metrics</TabsTrigger>
          <TabsTrigger value="financial">Financial Summary</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {data.recentActivity.length > 0 ? (
                  data.recentActivity.map((activity, index) => (
                    <ActivityItem
                      key={index}
                      title={activity.title}
                      description={activity.description}
                      time={activity.time}
                      type={activity.type}
                    />
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    No recent activity
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <PerformerItem
                  name="Sarah Johnson"
                  role="Sales Manager"
                  metric="$125,000"
                  progress={85}
                  target="$147,000"
                />
                <PerformerItem
                  name="Mike Chen"
                  role="Project Manager"
                  metric="23 Projects"
                  progress={92}
                  target="25 Projects"
                />
                <PerformerItem
                  name="Lisa Rodriguez"
                  role="Sales Rep"
                  metric="$89,500"
                  progress={74}
                  target="$120,000"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Sales Pipeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <PipelineStage stage="Leads" count={45} value="$890,000" />
                  <PipelineStage stage="Prospects" count={28} value="$650,000" />
                  <PipelineStage stage="Proposals" count={15} value="$420,000" />
                  <PipelineStage stage="Closed Won" count={8} value="$180,000" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lead Sources</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <SourceItem source="Referrals" percentage={35} count={42} />
                  <SourceItem source="Online Marketing" percentage={28} count={34} />
                  <SourceItem source="Direct Mail" percentage={20} count={24} />
                  <SourceItem source="Trade Shows" percentage={17} count={20} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <MetricItem label="Lead to Prospect" value="62.2%" trend="+5.1%" />
                  <MetricItem label="Prospect to Proposal" value="53.6%" trend="+2.8%" />
                  <MetricItem label="Proposal to Close" value="53.3%" trend="-1.2%" />
                  <MetricItem label="Overall Conversion" value="17.8%" trend="+3.2%" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="production" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <StatusItem status="In Progress" count={45} color="blue" />
                  <StatusItem status="Scheduled" count={23} color="yellow" />
                  <StatusItem status="Completed" count={156} color="green" />
                  <StatusItem status="On Hold" count={8} color="red" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Production Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <MetricItem label="Avg Project Duration" value="14.2 days" trend="-2.1 days" />
                  <MetricItem label="On-Time Completion" value="87.5%" trend="+4.2%" />
                  <MetricItem label="Customer Satisfaction" value="4.8/5" trend="+0.2" />
                  <MetricItem label="Rework Rate" value="3.2%" trend="-1.1%" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <RevenueItem category="Residential" amount="$524,000" percentage={62} />
                  <RevenueItem category="Commercial" amount="$245,000" percentage={29} />
                  <RevenueItem category="Repairs" amount="$78,000" percentage={9} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Accounts Receivable</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <ARItem period="0-30 days" amount="$125,000" percentage={65} />
                  <ARItem period="31-60 days" amount="$45,000" percentage={23} />
                  <ARItem period="61-90 days" amount="$18,000" percentage={9} />
                  <ARItem period="90+ days" amount="$6,000" percentage={3} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profitability</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <MetricItem label="Gross Margin" value="42.5%" trend="+1.8%" />
                  <MetricItem label="Net Profit Margin" value="18.2%" trend="+2.1%" />
                  <MetricItem label="EBITDA" value="$154,000" trend="+12.5%" />
                  <MetricItem label="ROI" value="24.8%" trend="+3.2%" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Component Definitions
interface KPICardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative';
  icon: React.ReactNode;
  description: string;
}

function KPICard({ title, value, change, changeType, icon, description }: KPICardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span className={changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
            {change}
          </span>
          <span>{description}</span>
        </div>
      </CardContent>
    </Card>
  );
}

interface ActivityItemProps {
  title: string;
  description: string;
  time: string;
  type: 'project' | 'quote' | 'payment' | 'report';
}

function ActivityItem({ title, description, time, type }: ActivityItemProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'project': return 'bg-blue-100 text-blue-800';
      case 'quote': return 'bg-green-100 text-green-800';
      case 'payment': return 'bg-yellow-100 text-yellow-800';
      case 'report': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex items-start space-x-3">
      <Badge variant="secondary" className={`${getTypeColor(type)} text-xs`}>
        {type}
      </Badge>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium">{title}</p>
        <p className="text-xs text-muted-foreground">{description}</p>
      </div>
      <span className="text-xs text-muted-foreground">{time}</span>
    </div>
  );
}

interface PerformerItemProps {
  name: string;
  role: string;
  metric: string;
  progress: number;
  target: string;
}

function PerformerItem({ name, role, metric, progress, target }: PerformerItemProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium">{name}</p>
          <p className="text-xs text-muted-foreground">{role}</p>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium">{metric}</p>
          <p className="text-xs text-muted-foreground">of {target}</p>
        </div>
      </div>
      <Progress value={progress} className="h-2" />
    </div>
  );
}

interface PipelineStageProps {
  stage: string;
  count: number;
  value: string;
}

function PipelineStage({ stage, count, value }: PipelineStageProps) {
  return (
    <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
      <div>
        <p className="text-sm font-medium">{stage}</p>
        <p className="text-xs text-muted-foreground">{count} items</p>
      </div>
      <p className="text-sm font-bold">{value}</p>
    </div>
  );
}

interface SourceItemProps {
  source: string;
  percentage: number;
  count: number;
}

function SourceItem({ source, percentage, count }: SourceItemProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm">{source}</span>
        <span className="text-sm font-medium">{percentage}%</span>
      </div>
      <div className="flex items-center space-x-2">
        <Progress value={percentage} className="h-2 flex-1" />
        <span className="text-xs text-muted-foreground">{count}</span>
      </div>
    </div>
  );
}

interface MetricItemProps {
  label: string;
  value: string;
  trend: string;
}

function MetricItem({ label, value, trend }: MetricItemProps) {
  const isPositive = trend.startsWith('+');
  return (
    <div className="flex items-center justify-between">
      <span className="text-sm text-muted-foreground">{label}</span>
      <div className="text-right">
        <p className="text-sm font-medium">{value}</p>
        <p className={`text-xs ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
          {trend}
        </p>
      </div>
    </div>
  );
}

interface StatusItemProps {
  status: string;
  count: number;
  color: 'blue' | 'yellow' | 'green' | 'red';
}

function StatusItem({ status, count, color }: StatusItemProps) {
  const getColorClass = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-100 text-blue-800';
      case 'yellow': return 'bg-yellow-100 text-yellow-800';
      case 'green': return 'bg-green-100 text-green-800';
      case 'red': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
      <div className="flex items-center space-x-2">
        <Badge variant="secondary" className={`${getColorClass(color)} text-xs`}>
          {status}
        </Badge>
      </div>
      <span className="text-sm font-bold">{count}</span>
    </div>
  );
}

interface RevenueItemProps {
  category: string;
  amount: string;
  percentage: number;
}

function RevenueItem({ category, amount, percentage }: RevenueItemProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm">{category}</span>
        <span className="text-sm font-medium">{amount}</span>
      </div>
      <Progress value={percentage} className="h-2" />
    </div>
  );
}

interface ARItemProps {
  period: string;
  amount: string;
  percentage: number;
}

function ARItem({ period, amount, percentage }: ARItemProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm">{period}</span>
        <span className="text-sm font-medium">{amount}</span>
      </div>
      <Progress value={percentage} className="h-2" />
    </div>
  );
}
