'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Plus, Save, Download, Share, Filter, Bar<PERSON>hart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileText, Settings } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { ReportBuilder } from '@/lib/reports/page/report-builder-simple';

interface Report {
  id: string;
  name: string;
  description: string;
  type: 'financial' | 'operational' | 'sales' | 'custom';
  lastModified: string;
  createdBy: string;
  isPublic: boolean;
  recordCount: number;
}

export default function CustomReportsPage() {
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const sampleReports: Report[] = [
    {
      id: '1',
      name: 'Permit Payment Analysis',
      description: 'Analysis of permit payments by team member and location',
      type: 'financial',
      lastModified: '05/14/2024 2:24 PM',
      createdBy: 'Sai Kiran Srisal',
      isPublic: false,
      recordCount: 334
    },
    {
      id: '2',
      name: 'Job Status Overview',
      description: 'Current status of all active jobs and milestones',
      type: 'operational',
      lastModified: '05/13/2024 4:15 PM',
      createdBy: 'Faith Hambrook',
      isPublic: true,
      recordCount: 156
    },
    {
      id: '3',
      name: 'Sales Performance Report',
      description: 'Monthly sales performance by representative',
      type: 'sales',
      lastModified: '05/12/2024 10:30 AM',
      createdBy: 'Eric Mitchell',
      isPublic: true,
      recordCount: 89
    },
    {
      id: '4',
      name: 'Customer Lead Sources',
      description: 'Analysis of lead sources and conversion rates',
      type: 'sales',
      lastModified: '05/11/2024 3:45 PM',
      createdBy: 'Patricia Vito',
      isPublic: false,
      recordCount: 267
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'financial': return 'bg-green-100 text-green-800';
      case 'operational': return 'bg-blue-100 text-blue-800';
      case 'sales': return 'bg-purple-100 text-purple-800';
      case 'custom': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const openReportBuilder = (report: Report) => {
    setSelectedReport(report);
    setActiveModal('report-builder');
  };

  const closeModal = () => {
    setActiveModal(null);
    setSelectedReport(null);
  };

  const createNewReport = () => {
    const newReport: Report = {
      id: 'new',
      name: 'New Custom Report',
      description: 'Build your custom report with advanced filtering and grouping',
      type: 'custom',
      lastModified: new Date().toLocaleString(),
      createdBy: 'Current User',
      isPublic: false,
      recordCount: 0
    };
    openReportBuilder(newReport);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/report">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reports
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Custom Report Builder</h1>
            <p className="text-muted-foreground">Create professional reports with AccuLynx-style interface</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button size="sm" onClick={createNewReport}>
            <Plus className="h-4 w-4 mr-2" />
            New Report
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All Reports</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="operational">Operational</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="custom">Custom</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sampleReports.map(report => (
              <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle className="text-lg font-medium text-gray-900">
                          {report.name}
                        </CardTitle>
                        <Badge className={`text-xs mt-1 ${getTypeColor(report.type)}`}>
                          {report.type.charAt(0).toUpperCase() + report.type.slice(1)}
                        </Badge>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openReportBuilder(report)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{report.description}</p>

                  <div className="space-y-2 text-xs text-gray-500">
                    <div className="flex justify-between">
                      <span>Records:</span>
                      <span className="font-medium">{report.recordCount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Created by:</span>
                      <span className="font-medium">{report.createdBy}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Last modified:</span>
                      <span className="font-medium">{report.lastModified}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Visibility:</span>
                      <Badge variant={report.isPublic ? 'default' : 'secondary'} className="text-xs">
                        {report.isPublic ? 'Public' : 'Private'}
                      </Badge>
                    </div>
                  </div>

                  <div className="mt-4 flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1"
                      onClick={() => openReportBuilder(report)}
                    >
                      Edit Report
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Other tab contents would filter the reports by type */}
        <TabsContent value="financial">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sampleReports.filter(r => r.type === 'financial').map(report => (
              <Card key={report.id} className="hover:shadow-lg transition-shadow">
                {/* Same card content as above */}
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Similar for other tabs */}
      </Tabs>

      {/* Quick Actions */}
      <div className="mt-12">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={createNewReport}>
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Create Custom Report</h3>
              <p className="text-sm text-gray-600">Build a report from scratch with AccuLynx-style interface</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <FileText className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Import Template</h3>
              <p className="text-sm text-gray-600">Start with a pre-built template and customize as needed</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Share className="h-8 w-8 text-purple-600 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Scheduled Reports</h3>
              <p className="text-sm text-gray-600">Set up automated report generation and delivery</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modals */}
      {activeModal === 'report-builder' && selectedReport && (
        <ReportBuilder
          reportTitle={selectedReport.name}
          reportData={[]}
          onClose={closeModal}
        />
      )}
    </div>
  );
}


