import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, Plus, Save, Download, Share, Filter, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default async function CustomReportsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/report">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reports
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Custom Report Builder</h1>
            <p className="text-muted-foreground">Create and customize your own reports</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </Button>
          <Button size="sm">
            <Download className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Report Configuration */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Report Configuration</CardTitle>
              <CardDescription>Set up your custom report parameters</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="report-name">Report Name</Label>
                <Input id="report-name" placeholder="Enter report name" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="report-description">Description</Label>
                <Input id="report-description" placeholder="Brief description" />
              </div>

              <div className="space-y-2">
                <Label>Data Source</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select data source" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="projects">Projects</SelectItem>
                    <SelectItem value="customers">Customers</SelectItem>
                    <SelectItem value="quotes">Quotes</SelectItem>
                    <SelectItem value="payments">Payments</SelectItem>
                    <SelectItem value="users">Users</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Chart Type</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button variant="outline" size="sm" className="flex flex-col items-center p-3">
                    <BarChart3 className="h-4 w-4 mb-1" />
                    <span className="text-xs">Bar</span>
                  </Button>
                  <Button variant="outline" size="sm" className="flex flex-col items-center p-3">
                    <PieChart className="h-4 w-4 mb-1" />
                    <span className="text-xs">Pie</span>
                  </Button>
                  <Button variant="outline" size="sm" className="flex flex-col items-center p-3">
                    <LineChart className="h-4 w-4 mb-1" />
                    <span className="text-xs">Line</span>
                  </Button>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label>Date Range</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select date range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="last-7-days">Last 7 days</SelectItem>
                    <SelectItem value="last-30-days">Last 30 days</SelectItem>
                    <SelectItem value="last-90-days">Last 90 days</SelectItem>
                    <SelectItem value="this-year">This year</SelectItem>
                    <SelectItem value="custom">Custom range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
              <CardDescription>Apply filters to your data</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Status</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="active" />
                    <Label htmlFor="active" className="text-sm">Active</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="completed" />
                    <Label htmlFor="completed" className="text-sm">Completed</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="pending" />
                    <Label htmlFor="pending" className="text-sm">Pending</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Location</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All locations" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    <SelectItem value="north">North Region</SelectItem>
                    <SelectItem value="south">South Region</SelectItem>
                    <SelectItem value="east">East Region</SelectItem>
                    <SelectItem value="west">West Region</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Assigned To</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All users" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    <SelectItem value="sarah">Sarah Johnson</SelectItem>
                    <SelectItem value="mike">Mike Chen</SelectItem>
                    <SelectItem value="lisa">Lisa Rodriguez</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Preview */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Report Preview</CardTitle>
              <CardDescription>Preview your custom report</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="chart" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="chart">Chart View</TabsTrigger>
                  <TabsTrigger value="table">Table View</TabsTrigger>
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                </TabsList>

                <TabsContent value="chart" className="space-y-4">
                  <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">Chart preview will appear here</p>
                      <p className="text-sm text-muted-foreground">Configure your report settings to see the preview</p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="table" className="space-y-4">
                  <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="h-12 w-12 bg-muted-foreground/20 rounded mx-auto mb-2 flex items-center justify-center">
                        <div className="grid grid-cols-3 gap-1">
                          {[...Array(9)].map((_, i) => (
                            <div key={i} className="w-1 h-1 bg-muted-foreground/40 rounded-full" />
                          ))}
                        </div>
                      </div>
                      <p className="text-muted-foreground">Table preview will appear here</p>
                      <p className="text-sm text-muted-foreground">Configure your report settings to see the data</p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="summary" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold">--</div>
                        <p className="text-sm text-muted-foreground">Total Records</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold">--</div>
                        <p className="text-sm text-muted-foreground">Average Value</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold">--</div>
                        <p className="text-sm text-muted-foreground">Total Value</p>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Report Actions</CardTitle>
              <CardDescription>Save, share, or schedule your report</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button className="w-full">
                  <Save className="h-4 w-4 mr-2" />
                  Save Report
                </Button>
                <Button variant="outline" className="w-full">
                  <Share className="h-4 w-4 mr-2" />
                  Share Report
                </Button>
                <Button variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Custom Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Custom Reports</CardTitle>
          <CardDescription>Your previously created custom reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <CustomReportItem
              name="Monthly Sales Performance"
              description="Sales metrics by team member for the current month"
              lastModified="2 days ago"
              status="Published"
            />
            <CustomReportItem
              name="Project Completion Analysis"
              description="Analysis of project completion times and efficiency"
              lastModified="1 week ago"
              status="Draft"
            />
            <CustomReportItem
              name="Customer Satisfaction Trends"
              description="Customer satisfaction scores over time"
              lastModified="2 weeks ago"
              status="Scheduled"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface CustomReportItemProps {
  name: string;
  description: string;
  lastModified: string;
  status: 'Published' | 'Draft' | 'Scheduled';
}

function CustomReportItem({ name, description, lastModified, status }: CustomReportItemProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Published': return 'bg-green-100 text-green-800';
      case 'Draft': return 'bg-yellow-100 text-yellow-800';
      case 'Scheduled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-1">
          <h4 className="font-medium">{name}</h4>
          <Badge variant="secondary" className={`${getStatusColor(status)} text-xs`}>
            {status}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">{description}</p>
        <p className="text-xs text-muted-foreground mt-1">Modified {lastModified}</p>
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm">Edit</Button>
        <Button variant="ghost" size="sm">View</Button>
      </div>
    </div>
  );
}
