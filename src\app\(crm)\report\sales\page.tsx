import { ReportPage } from '@/lib';

import { getData } from './helpers';

interface PageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function ReportSales({ searchParams }: PageProps) {
  const { data, chart } = await getData({ searchParams });

  return (
    <ReportPage
      data={data}
      axis={chart}
      title="Sales"
      description="Total sales and revenue over time."
    />
  );
}
