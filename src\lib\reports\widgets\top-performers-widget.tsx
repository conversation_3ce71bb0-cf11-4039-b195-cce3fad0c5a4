import { Target } from 'lucide-react';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

export async function TopPerformersWidget() {
  // In a real implementation, this would fetch data from your database
  const performers = [
    {
      name: '<PERSON>',
      role: 'Sales Manager',
      metric: '$125,000',
      progress: 85,
      target: '$147,000'
    },
    {
      name: '<PERSON>',
      role: 'Project Manager',
      metric: '23 Projects',
      progress: 92,
      target: '25 Projects'
    },
    {
      name: '<PERSON>',
      role: 'Sales Rep',
      metric: '$89,500',
      progress: 74,
      target: '$120,000'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Target className="h-5 w-5 mr-2" />
          Top Performers
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {performers.map((performer, index) => (
          <div key={index} className="space-y-2">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">{performer.name}</p>
                <p className="text-xs text-muted-foreground">{performer.role}</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">{performer.metric}</p>
                <p className="text-xs text-muted-foreground">of {performer.target}</p>
              </div>
            </div>
            <Progress value={performer.progress} className="h-2" />
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
