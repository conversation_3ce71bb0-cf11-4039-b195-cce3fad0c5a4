import { Target } from 'lucide-react';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

import { getUserModel, getProjectModel } from '@/schemas';
import { validateRequest } from '@/server';

export async function TopPerformersWidget() {
  const { user, account } = await validateRequest();
  if (!user || !account) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Top Performers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">
            Unable to load performance data
          </div>
        </CardContent>
      </Card>
    );
  }

  const userModel = await getUserModel();
  const projectModel = await getProjectModel();

  // Get users with their project counts
  const users = await userModel.find({ account: account._id }).limit(3);

  const performers = await Promise.all(
    users.map(async (user) => {
      const projectCount = await projectModel.countDocuments({
        account: account._id,
        assignedTo: user._id
      });

      return {
        name: user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unknown User',
        role: user.role || 'Team Member',
        metric: `${projectCount} Projects`,
        progress: Math.min(projectCount * 10, 100), // Simple progress calculation
        target: `${Math.max(projectCount + 2, 5)} Projects`
      };
    })
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Target className="h-5 w-5 mr-2" />
          Top Performers
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {performers.length > 0 ? (
          performers.map((performer, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">{performer.name}</p>
                  <p className="text-xs text-muted-foreground">{performer.role}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{performer.metric}</p>
                  <p className="text-xs text-muted-foreground">of {performer.target}</p>
                </div>
              </div>
              <Progress value={performer.progress} className="h-2" />
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            No performance data available
          </div>
        )}
      </CardContent>
    </Card>
  );
}
