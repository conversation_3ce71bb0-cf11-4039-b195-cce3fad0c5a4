import { DollarSign, TrendingUp, Users, BarChart3 } from 'lucide-react';

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

export async function KPISummaryWidget() {
  // In a real implementation, this would fetch data from your database
  const kpis = [
    {
      title: 'Total Revenue',
      value: '$847,392',
      change: '+12.5%',
      changeType: 'positive' as const,
      icon: DollarSign,
      description: 'vs last month'
    },
    {
      title: 'Active Projects',
      value: '156',
      change: '+8',
      changeType: 'positive' as const,
      icon: BarChart3,
      description: 'new this month'
    },
    {
      title: 'Conversion Rate',
      value: '68.4%',
      change: '+2.1%',
      changeType: 'positive' as const,
      icon: TrendingUp,
      description: 'leads to projects'
    },
    {
      title: 'Team Members',
      value: '24',
      change: '+2',
      changeType: 'positive' as const,
      icon: Users,
      description: 'active users'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {kpis.map((kpi) => (
        <Card key={kpi.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
            <kpi.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpi.value}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <span className={kpi.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
                {kpi.change}
              </span>
              <span>{kpi.description}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
