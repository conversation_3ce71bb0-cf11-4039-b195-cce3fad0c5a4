import { DollarSign, TrendingUp, Users, BarChart3 } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { getProjectModel, getUserModel } from '@/schemas';
import { validateRequest } from '@/server';

export async function KPISummaryWidget() {
  const { user, account } = await validateRequest();
  if (!user || !account) {
    return <div>Unable to load KPI data</div>;
  }

  const projectModel = await getProjectModel();
  const userModel = await getUserModel();

  const [totalProjects, activeUsers] = await Promise.all([
    projectModel.countDocuments({ account: account._id }),
    userModel.countDocuments({ account: account._id })
  ]);

  const kpis = [
    {
      title: 'Total Projects',
      value: totalProjects.toString(),
      change: '+0',
      changeType: 'positive' as const,
      icon: BarChart3,
      description: 'all time'
    },
    {
      title: 'Active Users',
      value: activeUsers.toString(),
      change: '+0',
      changeType: 'positive' as const,
      icon: Users,
      description: 'team members'
    },
    {
      title: 'Reports Available',
      value: '10',
      change: '+0',
      changeType: 'positive' as const,
      icon: DollarSign,
      description: 'report types'
    },
    {
      title: 'System Status',
      value: 'Active',
      change: '100%',
      changeType: 'positive' as const,
      icon: TrendingUp,
      description: 'uptime'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {kpis.map((kpi) => (
        <Card key={kpi.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
            <kpi.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpi.value}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <span className={kpi.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
                {kpi.change}
              </span>
              <span>{kpi.description}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
