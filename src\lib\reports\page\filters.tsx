'use client';

import { useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

// Static data to avoid server-side import issues
const dateRangeOptions = [
  { label: 'All Data', span: [new Date(0), new Date()] },
  { label: 'Today', span: [new Date(), new Date()] },
  { label: 'Yesterday', span: [new Date(Date.now() - 86400000), new Date(Date.now() - 86400000)] },
  { label: 'This Week', span: [new Date(Date.now() - 7 * 86400000), new Date()] },
  { label: 'This Month', span: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()] },
  { label: 'Last Month', span: [new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), new Date(new Date().getFullYear(), new Date().getMonth(), 0)] },
  { label: 'This Year', span: [new Date(new Date().getFullYear(), 0, 1), new Date()] },
  { label: 'Rolling 30 Days', span: [new Date(Date.now() - 30 * 86400000), new Date()] },
  { label: 'Rolling 90 Days', span: [new Date(Date.now() - 90 * 86400000), new Date()] },
];

const locationOptions = [
  { value: 'location1', label: 'Main Office' },
  { value: 'location2', label: 'Branch Office' },
  { value: 'location3', label: 'Warehouse' },
  { value: 'location4', label: 'Remote Site' },
];

export function Filters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get current values from URL
  const currentDateRange = searchParams.get('dateRange') || 'All Data';
  const currentLocations = searchParams.get('locations')?.split(',') || [];

  const [selectedDateRange, setSelectedDateRange] = useState(currentDateRange);
  const [selectedLocations, setSelectedLocations] = useState<string[]>(currentLocations);
  const [showLocationDropdown, setShowLocationDropdown] = useState(false);

  const handleLocationToggle = (locationValue: string) => {
    setSelectedLocations(prev =>
      prev.includes(locationValue)
        ? prev.filter(loc => loc !== locationValue)
        : [...prev, locationValue]
    );
  };

  const applyFilters = () => {
    const params = new URLSearchParams();

    // Handle date range
    if (selectedDateRange && selectedDateRange !== 'All Data') {
      const selectedRange = dateRangeOptions.find(r => r.label === selectedDateRange);
      if (selectedRange) {
        params.set('startDate', selectedRange.span[0].toISOString());
        params.set('endDate', selectedRange.span[1].toISOString());
        params.set('dateRange', selectedDateRange);
      }
    }

    // Handle locations
    if (selectedLocations.length > 0) {
      params.set('locations', selectedLocations.join(','));
    }

    // Navigate to the same page with new parameters
    router.push(`${pathname}?${params.toString()}`);
  };

  const clearFilters = () => {
    setSelectedDateRange('All Data');
    setSelectedLocations([]);
    router.push(pathname);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Date Range Filter */}
        <div className="space-y-2">
          <Label>Date Range</Label>
          <Select value={selectedDateRange} onValueChange={setSelectedDateRange}>
            <SelectTrigger>
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              {dateRangeOptions.map((range) => (
                <SelectItem key={range.label} value={range.label}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Location Filter */}
        <div className="space-y-2">
          <Label>Locations</Label>
          <Popover open={showLocationDropdown} onOpenChange={setShowLocationDropdown}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                {selectedLocations.length === 0 ? (
                  <span className="text-muted-foreground">All Locations</span>
                ) : (
                  <span>{selectedLocations.length} location(s) selected</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
              <div className="p-3 border-b">
                <div className="text-sm font-medium">Select Locations</div>
              </div>
              <div className="max-h-60 overflow-y-auto">
                {locationOptions.map((location) => (
                  <div
                    key={location.value}
                    className="flex items-center space-x-2 p-2 hover:bg-muted cursor-pointer"
                    onClick={() => handleLocationToggle(location.value)}
                  >
                    <Checkbox
                      checked={selectedLocations.includes(location.value)}
                      onChange={() => handleLocationToggle(location.value)}
                    />
                    <span className="text-sm">{location.label}</span>
                  </div>
                ))}
              </div>
              {selectedLocations.length > 0 && (
                <div className="p-3 border-t">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedLocations([]);
                    }}
                  >
                    Clear All
                  </Button>
                </div>
              )}
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex gap-2">
        <Button onClick={applyFilters}>Apply Filters</Button>
        <Button variant="secondary" onClick={clearFilters}>Clear Filters</Button>
      </div>
    </div>
  );
}
