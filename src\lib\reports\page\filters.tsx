import { Field } from '@/lib/form';

import { getDateRange, getLocations } from './helpers';

export async function Filters() {
  const range = getDateRange();
  const { locations } = await getLocations();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Field
        label="Date Range"
        name="dateRange"
        options={range.map((r) => r.label)}
        type="select"
        value={range[0].label}
      />

      <Field
        label="Location"
        name="location"
        options={locations}
        type="select"
        multiple={true}
        placeholder="All Locations"
      />
    </div>
  );
}
