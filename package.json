{"name": "trussi-ai-crm", "version": "0.0.9", "private": true, "engines": {"node": "^20 || ^21 || ^22"}, "scripts": {"postinstall": "cross-os postinstall", "dev": "nodemon", "build": "next build && tsc --project tsconfig.server.json", "start": "cross-env NODE_ENV=production node dist/server.js", "prettier:fix": "npx prettier . --write", "prettier:check": "npx prettier --check .", "eslint": "eslint .", "eslint:fix": "eslint . --fix", "tslint": "tsc --noEmit", "lint": "npm run prettier:check && npm run eslint && npm run tslint", "prepare": "husky", "test": "jest", "test:watch": "jest --watch", "test:e2e": "npx playwright test", "test:e2e:ui": "npx playwright test --ui"}, "cross-os": {"postinstall": {"win32": "ECHO \"WIN32\"", "linux": "chmod +x ./bin/dev", "darwin": "chmod +x ./bin/dev"}, "dev": {"win32": ".\\bin\\dev", "linux": "./bin/dev", "darwin": "./bin/dev"}}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@mobiscroll/react": "^5.32.1", "@next/third-parties": "^15.2.4", "@node-rs/argon2": "^1.8.3", "@node-rs/bcrypt": "^1.10.4", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@tanstack/react-table": "^8.21.3", "@wojtekmaj/react-hooks": "^1.22.0", "autoprefixer": "^10.4.19", "chart.js": "^4.4.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "formidable": "^3.5.2", "he": "^1.2.0", "intuit-oauth": "^4.2.0", "isomorphic-dompurify": "^2.14.0", "jose": "^5.6.2", "lucia": "^3.2.0", "lucide-react": "^0.408.0", "mongoose": "^8.5.3", "mongoose-schema-jsonschema": "^2.2.0", "next": "14.2.20", "node-cron": "^3.0.3", "nodemailer": "^6.9.13", "ol": "^10.3.1", "oslo": "^1.2.0", "pdfjs-dist": "^5.1.91", "postcss": "^8", "quill": "^2.0.2", "quill-mention": "^6.0.1", "raw-loader": "^4.0.2", "react": "^18", "react-datepicker": "^8.2.1", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-google-places-autocomplete": "^4.1.0", "react-pdf": "^9.2.1", "react-to-print": "^3.0.4", "recharts": "^2.15.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "string-strip-html": "^13.4.8", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@faker-js/faker": "^8.4.1", "@playwright/test": "^1.44.1", "@types/formidable": "^3.4.5", "@types/he": "^1.2.3", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "cross-os": "^1.5.0", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-no-type-assertion": "^1.3.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-tailwindcss": "^3.15.2", "eslint-plugin-unused-imports": "^3.0.0", "husky": "^9.0.11", "nodemon": "^3.1.9", "prettier": "^3.2.5", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "typescript": "5.5.4"}, "optionalDependencies": {"@node-rs/argon2-linux-x64-gnu": "^1.8.3", "@node-rs/argon2-win32-x64-msvc": "^1.8.3", "@node-rs/bcrypt-linux-x64-gnu": "^1.10.4", "@node-rs/bcrypt-win32-x64-msvc": "^1.10.4"}}