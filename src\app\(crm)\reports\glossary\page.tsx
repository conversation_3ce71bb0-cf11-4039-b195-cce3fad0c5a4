﻿import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, Calculator, Info } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function ReportsGlossaryPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/report" className="flex items-center text-sm text-muted-foreground hover:text-foreground">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Reports
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Reports Glossary</h1>
            <p className="text-muted-foreground">Definitions, calculations, and explanations for all report terms</p>
          </div>
        </div>
      </div>

      {/* Alphabet Navigation */}
      <div className="flex flex-wrap gap-2 p-4 bg-muted rounded-lg">
        <p className="text-sm text-muted-foreground w-full mb-2">Quick Navigation:</p>
        {Array.from('ABCDEFGHIJKLMNOPQRSTUVWXYZ').map((letter) => (
          <a
            key={letter}
            href={`#section-${letter}`}
            className="flex items-center justify-center h-8 w-8 text-sm font-medium rounded-md hover:bg-background transition-colors"
          >
            {letter}
          </a>
        ))}
      </div>

      {/* Glossary Content */}
      <div className="space-y-6">
        {/* A Section */}
        <div id="section-A">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">A</span>
            A
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Active Projects"
              definition="Projects that are currently in progress and not completed, cancelled, or marked as dead."
              calculation="COUNT(projects WHERE isDead ≠ true AND isCancelled ≠ true AND milestones.name ≠ 'COMPLETED')"
              category="metric"
              example="If you have 50 total projects, 30 completed, 5 cancelled, and 3 dead, then Active Projects = 12"
            />
            <GlossaryCard
              term="Active Users"
              definition="Total number of user accounts in your organization that have access to the system."
              calculation="COUNT(users WHERE account = current_account)"
              category="metric"
              example="All team members with login credentials"
            />
          </div>
        </div>

        {/* C Section */}
        <div id="section-C">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">C</span>
            C
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Closing Percentage"
              definition="The percentage of leads or prospects that convert into completed projects."
              calculation="(Completed Projects / Total Projects) × 100"
              category="metric"
              example="If you have 100 projects and 75 are completed: 75/100 × 100 = 75%"
            />
            <GlossaryCard
              term="Conversion Rate"
              definition="The rate at which leads or prospects convert to paying customers or completed projects."
              calculation="(Successful Outcomes / Total Opportunities) × 100"
              category="metric"
              example="Same as Closing Percentage in most contexts"
            />
          </div>
        </div>

        {/* D Section */}
        <div id="section-D">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">D</span>
            D
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Dead Leads"
              definition="Prospects or potential projects that are no longer viable or have been lost to competitors."
              calculation="COUNT(projects WHERE milestones.name = 'DEAD')"
              category="metric"
              example="Leads that didn't convert due to budget, timing, or other factors"
            />
            <GlossaryCard
              term="Dashboard"
              definition="A visual interface displaying key performance indicators, metrics, and real-time business data."
              calculation="Aggregated data from multiple sources"
              category="report"
              example="Overview showing revenue, active projects, and recent activity"
            />
          </div>
        </div>

        {/* R Section */}
        <div id="section-R">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">R</span>
            R
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Revenue"
              definition="Total income generated from completed projects and services."
              calculation="SUM(project_value WHERE status = 'completed')"
              category="metric"
              example="All money earned from finished projects"
            />
            <GlossaryCard
              term="Reports"
              definition="Structured presentations of business data and analytics for decision-making."
              calculation="Various data aggregations and visualizations"
              category="report"
              example="Sales reports, financial reports, project reports"
            />
          </div>
        </div>

        {/* E Section */}
        <div id="section-E">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">E</span>
            E
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Estimated Revenue"
              definition="Projected total revenue based on average project values and current project count."
              calculation="Total Projects × Average Project Value"
              category="calculation"
              example="100 projects × $25,000 average = $2,500,000 estimated revenue"
            />
            <GlossaryCard
              term="Expenses Report"
              definition="Detailed breakdown of business expenses categorized by type, project, or time period."
              calculation="SUM(expenses) grouped by category/period"
              category="report"
              example="Monthly expenses for materials, labor, equipment, etc."
            />
          </div>
        </div>

        {/* G Section */}
        <div id="section-G">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">G</span>
            G
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Growth Rate"
              definition="Percentage increase or decrease in a metric over a specific time period."
              calculation="((New Value - Old Value) / Old Value) × 100"
              category="calculation"
              example="Revenue grew from $100k to $120k = 20% growth rate"
            />
            <GlossaryCard
              term="Glossary"
              definition="Comprehensive reference guide explaining all terms, calculations, and concepts used in reports."
              calculation="Alphabetical organization of definitions"
              category="metadata"
              example="This page you're currently viewing"
            />
          </div>
        </div>

        {/* L Section */}
        <div id="section-L">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">L</span>
            L
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Lead Source Report"
              definition="Analysis of where your leads and customers are coming from (referrals, marketing, etc.)."
              calculation="COUNT(projects) grouped by leadSource"
              category="report"
              example="40% referrals, 30% online marketing, 20% direct mail, 10% trade shows"
            />
            <GlossaryCard
              term="Last Updated"
              definition="Timestamp showing when a report or data was last refreshed or modified."
              calculation="MAX(modified_date) for the dataset"
              category="metadata"
              example="'2 hours ago' or '2024-12-11 14:30:00'"
            />
          </div>
        </div>

        {/* P Section */}
        <div id="section-P">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">P</span>
            P
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Payments Received Report"
              definition="Summary of all payments received from customers, organized by date, amount, and payment method."
              calculation="SUM(payments) grouped by period/customer"
              category="report"
              example="Monthly cash flow showing all customer payments"
            />
            <GlossaryCard
              term="Profitability Report"
              definition="Analysis of profit margins by project, showing revenue minus costs."
              calculation="(Revenue - Costs) / Revenue × 100"
              category="report"
              example="Project with $50k revenue and $35k costs = 30% profit margin"
            />
          </div>
        </div>

        {/* S Section */}
        <div id="section-S">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">S</span>
            S
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Sales Report"
              definition="Comprehensive analysis of sales performance, including revenue, conversion rates, and trends."
              calculation="Multiple metrics combined"
              category="report"
              example="Monthly sales summary with targets vs. actual performance"
            />
            <GlossaryCard
              term="Success Rate"
              definition="Percentage of projects that are completed successfully versus total projects started."
              calculation="(Completed Projects / Total Projects) × 100"
              category="metric"
              example="Same as Conversion Rate or Closing Percentage"
            />
          </div>
        </div>

        {/* T Section */}
        <div id="section-T">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mr-3">T</span>
            T
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <GlossaryCard
              term="Total Projects"
              definition="Complete count of all projects in the system, regardless of status."
              calculation="COUNT(projects WHERE account = current_account)"
              category="metric"
              example="Includes active, completed, cancelled, and dead projects"
            />
            <GlossaryCard
              term="Top Performers"
              definition="Team members or entities with the highest performance metrics in a given period."
              calculation="ORDER BY performance_metric DESC"
              category="ranking"
              example="Sales reps with most closed deals or highest revenue"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

interface GlossaryCardProps {
  term: string;
  definition: string;
  calculation: string;
  category: 'metric' | 'report' | 'calculation' | 'automation' | 'collaboration' | 'ranking' | 'usage' | 'feed' | 'visualization' | 'metadata';
  example: string;
}

function GlossaryCard({ term, definition, calculation, category, example }: GlossaryCardProps) {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'metric': return 'bg-blue-100 text-blue-800';
      case 'report': return 'bg-green-100 text-green-800';
      case 'calculation': return 'bg-purple-100 text-purple-800';
      case 'automation': return 'bg-orange-100 text-orange-800';
      case 'collaboration': return 'bg-pink-100 text-pink-800';
      case 'ranking': return 'bg-yellow-100 text-yellow-800';
      case 'usage': return 'bg-gray-100 text-gray-800';
      case 'feed': return 'bg-indigo-100 text-indigo-800';
      case 'visualization': return 'bg-teal-100 text-teal-800';
      case 'metadata': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg">{term}</CardTitle>
          <Badge variant="secondary" className={`${getCategoryColor(category)} text-xs`}>
            {category}
          </Badge>
        </div>
        <CardDescription>{definition}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <div>
          <h5 className="font-semibold text-sm mb-1 flex items-center">
            <Calculator className="h-3 w-3 mr-1" />
            Calculation
          </h5>
          <code className="text-xs bg-muted p-2 rounded block">{calculation}</code>
        </div>
        <div>
          <h5 className="font-semibold text-sm mb-1 flex items-center">
            <Info className="h-3 w-3 mr-1" />
            Example
          </h5>
          <p className="text-sm text-muted-foreground">{example}</p>
        </div>
      </CardContent>
    </Card>
  );
}
