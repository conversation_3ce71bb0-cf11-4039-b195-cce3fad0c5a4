import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

export async function SalesPipelineWidget() {
  // In a real implementation, this would fetch data from your database
  const pipelineStages = [
    { stage: 'Leads', count: 45, value: '$890,000', percentage: 100 },
    { stage: 'Prospects', count: 28, value: '$650,000', percentage: 73 },
    { stage: 'Proposals', count: 15, value: '$420,000', percentage: 47 },
    { stage: 'Closed Won', count: 8, value: '$180,000', percentage: 20 }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales Pipeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pipelineStages.map((stage) => (
            <div key={stage.stage} className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">{stage.stage}</p>
                  <p className="text-xs text-muted-foreground">{stage.count} items</p>
                </div>
                <p className="text-sm font-bold">{stage.value}</p>
              </div>
              <Progress value={stage.percentage} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
